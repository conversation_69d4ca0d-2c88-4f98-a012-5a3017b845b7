use crate::model::administrative_classes::administrative_classes::AdministrativeClasses;
use crate::model::teaching_classes::teaching_classes::TeachingClasses;
use crate::model::user::auth::*;
use crate::model::{Role, Teacher};
use crate::repository::user::identities::user_identity_repository::UserIdentityRepository;
use crate::utils::schema::{connect_with_schema, validate_schema_name};
use anyhow::Result;
use anyhow::{anyhow, bail};
use chrono::Utc;
use minio::s3::utils::utc_now;
use sqlx::{Acquire, PgPool, Row};
use std::collections::HashMap;
use std::sync::Arc;
use tracing::{info, warn};
use uuid::Uuid;

pub struct IdentityService {
    db: PgPool,
    repository: Arc<UserIdentityRepository>,
}

impl IdentityService {
    pub fn new(db: PgPool, repository: Arc<UserIdentityRepository>) -> Self {
        Self { db, repository }
    }

    /// 获取用户在指定租户schema中的所有身份信息
    pub async fn get_user_identities_in_schema(&self, user_id: Uuid, schema_name: &str) -> AuthResult<Vec<UserIdentity>> {
        let identities = self.repository
            .get_user_identities_in_schema_simple(user_id, schema_name)
            .await
            .map_err(|e| AuthError::DatabaseError(e.to_string()))?;

        Ok(identities)
    }

    /// 在指定租户schema中创建用户身份
    pub async fn create_user_identity(&self, user_id: Uuid, role_id: Uuid, target_type: &str, target_id: Option<Uuid>, subject: Option<&str>, schema_name: &str) -> AuthResult<UserIdentity> {
        let identity = self.repository
            .create_user_identity_simple(user_id, role_id, target_type, target_id, subject, schema_name)
            .await
            .map_err(|e| AuthError::DatabaseError(e.to_string()))?;

        Ok(identity)
    }

    /// 获取用户在指定租户schema中的特定身份
    pub async fn get_user_identity_in_schema(&self, user_id: Uuid, identity_id: Uuid, schema_name: &str) -> AuthResult<Option<UserIdentity>> {
        let identity = self.repository
            .get_user_identity_in_schema_simple(user_id, identity_id, schema_name)
            .await
            .map_err(|e| AuthError::DatabaseError(e.to_string()))?;

        Ok(identity)
    }

    /// 更新用户身份信息
    pub async fn update_user_identity(&self, identity_id: Uuid, target_type: Option<&str>, target_id: Option<Option<Uuid>>, subject: Option<Option<&str>>, schema_name: &str) -> AuthResult<()> {
        self.repository
            .update_user_identity_simple(identity_id, target_type, target_id, subject, schema_name)
            .await
            .map_err(|e| AuthError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    /// 删除用户身份
    pub async fn delete_user_identity(&self, identity_id: Uuid, schema_name: &str) -> AuthResult<()> {
        self.repository
            .delete_user_identity_simple(identity_id, schema_name)
            .await
            .map_err(|e| AuthError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    // Legacy compatibility methods (to be deprecated)
    pub async fn get_identity_suggestions(&self, _user_id: Uuid) -> AuthResult<Vec<IdentityBindingSuggestion>> {
        // Return empty suggestions for now - this feature will be reimplemented later
        warn!("get_identity_suggestions called - returning empty results (method deprecated)");
        Ok(Vec::new())
    }

    pub async fn bind_identity(&self, user_id: Uuid, request: BindIdentityRequest) -> Result<()> {
        // 获取角色映射
        let map = self.repository.get_roles_map().await?;

        // 获取schema名称
        let schema_name = self.repository.get_schema_name_by_tenant_id(request.tenant_id).await?;
        let safe_schema = validate_schema_name(&*schema_name)?;

        // 开始事务
        let mut conn = connect_with_schema(&self.db, &safe_schema).await?;
        let mut tx = conn.begin().await?;
        let mut user_identity_vec = vec![];
        match request.identity_type.as_str() {
            "student" => {
                let role = map.get(&"student".to_string()).ok_or_else(|| anyhow!("角色权限查询错误"))?;
                let student_number = request.extend_info.ok_or_else(|| anyhow!("无法获取学生信息"))?;

                // 使用 repository 查询学生ID
                let target_id = self.repository.get_student_id_by_number(&student_number, &safe_schema).await?;

                let time = utc_now();
                user_identity_vec.push(UserIdentity {
                    id: Uuid::new_v4(),
                    user_id,
                    role_id: role.id,
                    target_type: "student".to_string(),
                    target_id: Some(target_id),
                    subject: None,
                    created_at: time,
                    updated_at: time,
                });

                // 使用 repository 更新学生用户ID
                self.repository.update_student_user_id(target_id, user_id, &safe_schema).await?;
            }
            "teacher" => {
                // 使用 repository 查询教师信息
                let teacher = self.repository.get_teacher_by_user_id(user_id, &safe_schema).await?
                    .ok_or_else(|| anyhow::anyhow!("未查询到关联老师"))?;

                if teacher.user_id.is_some() {
                    bail!("老师已经被其他用户绑定")
                }

                let teacher_id = teacher.id;

                // 查询行政班信息
                let a_class_option = self.repository.get_administrative_class_by_teacher_id(teacher_id, &safe_schema).await?;

                if let Some(a_class) = a_class_option {
                    let role = map.get(&"class_teacher".to_string()).ok_or_else(|| anyhow!("角色权限查询错误"))?;
                    let time = utc_now();
                    user_identity_vec.push(UserIdentity {
                        id: Uuid::new_v4(),
                        user_id,
                        role_id: role.id,
                        target_type: "class".to_string(),
                        target_id: Some(a_class.id),
                        subject: None,
                        created_at: time,
                        updated_at: time,
                    })
                }

                // 查询教学班信息
                let t_class_option = self.repository.get_teaching_class_by_teacher_id(teacher_id, &safe_schema).await?;

                if let Some(t_class) = t_class_option {
                    let role = map.get(&"teacher".to_string()).ok_or_else(|| anyhow!("角色权限查询错误"))?;
                    let time = utc_now();
                    user_identity_vec.push(UserIdentity {
                        id: Uuid::new_v4(),
                        user_id,
                        role_id: role.id,
                        target_type: "teaching_class".to_string(),
                        target_id: Some(t_class.id),
                        subject: None,
                        created_at: time,
                        updated_at: time,
                    });
                }

                if !user_identity_vec.is_empty() {
                    // 使用 repository 更新教师用户ID
                    self.repository.update_teacher_user_id(teacher_id, user_id, &safe_schema).await?;
                } else {
                    bail!("教师信息未完成创建，暂时无法绑定");
                }
            }
            "parent" => {
                // todo 增加家长表信息
                let role = map.get(&"parent".to_string()).ok_or_else(|| anyhow!("角色权限查询错误"))?;
                let student_name = request.extend_info.ok_or_else(|| anyhow!("无法获取学生信息"))?;

                // 使用 repository 查询学生ID
                let target_id = self.repository.get_student_id_by_name(&student_name, &safe_schema).await?;

                let time = utc_now();
                user_identity_vec.push(UserIdentity {
                    id: Uuid::new_v4(),
                    user_id,
                    role_id: role.id,
                    target_type: "student".to_string(),
                    target_id: Some(target_id),
                    subject: None,
                    created_at: time,
                    updated_at: time,
                });
            }
            _ => {
                bail!("不支持的身份类型")
            }
        };

        let is_empty = user_identity_vec.is_empty();

        if !is_empty {
            // 使用 repository 批量插入用户身份
            self.repository.batch_insert_user_identities(user_identity_vec, &safe_schema, &mut tx).await?;

            // 使用 repository 创建用户租户链接
            self.repository.create_user_tenant_link(user_id, request.tenant_id, "member").await?;
        } else {
            bail!("账户信息未完成创建，暂时无法绑定");
        }

        tx.commit().await?;
        Ok(())
    }

    pub async fn switch_identity(
        &self,
        _user_id: Uuid,
        _session_id: Uuid,
        _request: SwitchIdentityRequest,
        _ip_address: Option<std::net::IpAddr>,
        _user_agent: Option<String>,
    ) -> AuthResult<SwitchIdentityResponse> {
        // This is a legacy method - for now return an error directing to use new session management
        warn!("switch_identity called - method deprecated, use new session management");
        Err(AuthError::InsufficientPermissions)
    }

    pub async fn get_identities(&self, user_id: Uuid, _username: String) -> AuthResult<Vec<IdentityInfo>> {
        let identities = self.repository
            .get_all_user_identities_across_tenants(user_id)
            .await
            .map_err(|e| AuthError::DatabaseError(e.to_string()))?;

        Ok(identities)
    }

    pub async fn verify_identity(&self, _identity_id: Uuid, _verified_by: Uuid) -> AuthResult<()> {
        warn!("verify_identity called - method deprecated");
        // This method needs to be reimplemented with tenant schema context
        Err(AuthError::InsufficientPermissions)
    }
}
